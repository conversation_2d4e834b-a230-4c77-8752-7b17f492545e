#!/usr/bin/env python3
"""
Test script for comprehensive thesis scoring system.

This script tests the new comprehensive scoring engine with sample data.
Run this after setting up the OpenAI API key to verify everything works.
"""

import asyncio
import json
from datetime import datetime, timezone
from typing import Dict, Any

from bson import ObjectId

# Import the comprehensive scoring engine
from app.services.thesis.scoring import ComprehensiveThesisScoringEngine
from app.models.thesis import (
    ScoringRule, 
    RuleType, 
    ConditionOperator, 
    FilterCondition,
    AggregationType
)
from app.models.form import QuestionType


class MockThesis:
    """Mock thesis object for testing."""
    
    def __init__(self):
        self.id = ObjectId()
        self.name = "Test Investment Thesis"
        self.scoring_rules = []


def create_sample_scoring_rules():
    """Create sample scoring rules for testing."""
    rules = []
    
    # Text question rule (will use AI scoring)
    text_rule = ScoringRule(
        id=ObjectId(),
        thesis_id=ObjectId(),
        rule_type=RuleType.SCORING,
        question_id=ObjectId(),
        question_type=QuestionType.LONG_TEXT,
        weight=2.0,
        condition=FilterCondition(
            question_id="text_question_1",
            operator=ConditionOperator.EQUALS,  # Not used for text questions
            value={
                "good": "We have a strong product-market fit with 40% month-over-month growth and high customer retention rates. Our customers love the product and are actively referring new users.",
                "bad": "We're still figuring out our product-market fit. Growth has been slow and we're not sure if customers really need our product."
            }
        ),
        notes="Product-market fit assessment"
    )
    rules.append(text_rule)
    
    # Numeric question rule
    numeric_rule = ScoringRule(
        id=ObjectId(),
        thesis_id=ObjectId(),
        rule_type=RuleType.SCORING,
        question_id=ObjectId(),
        question_type=QuestionType.NUMBER,
        weight=1.5,
        condition=FilterCondition(
            question_id="numeric_question_1",
            operator=ConditionOperator.GREATER_THAN,
            value=1000000  # Revenue > $1M
        ),
        notes="Revenue threshold"
    )
    rules.append(numeric_rule)
    
    # Single select question rule
    select_rule = ScoringRule(
        id=ObjectId(),
        thesis_id=ObjectId(),
        rule_type=RuleType.SCORING,
        question_id=ObjectId(),
        question_type=QuestionType.SINGLE_SELECT,
        weight=1.0,
        condition=FilterCondition(
            question_id="select_question_1",
            operator=ConditionOperator.IN,
            value=["Series A", "Series B", "Series C"]
        ),
        notes="Investment stage preference"
    )
    rules.append(select_rule)
    
    # Boolean question rule
    boolean_rule = ScoringRule(
        id=ObjectId(),
        thesis_id=ObjectId(),
        rule_type=RuleType.SCORING,
        question_id=ObjectId(),
        question_type=QuestionType.BOOLEAN,
        weight=0.5,
        condition=FilterCondition(
            question_id="boolean_question_1",
            operator=ConditionOperator.EQUALS,
            value=True
        ),
        notes="Has intellectual property"
    )
    rules.append(boolean_rule)
    
    # Bonus rule
    bonus_rule = ScoringRule(
        id=ObjectId(),
        thesis_id=ObjectId(),
        rule_type=RuleType.BONUS,
        bonus_points=10.0,
        condition=FilterCondition(
            question_id="bonus_question_1",
            operator=ConditionOperator.EQUALS,
            value="AI/ML"
        ),
        notes="AI/ML sector bonus"
    )
    rules.append(bonus_rule)
    
    return rules


def create_sample_form_responses():
    """Create sample form responses for testing."""
    return {
        "text_question_1": "Our startup has achieved strong product-market fit with 45% month-over-month growth. We have over 1000 active users who love our product, with a 95% retention rate. Customers are actively referring new users and we have a waiting list of 500+ potential customers.",
        "numeric_question_1": 2500000,  # $2.5M revenue
        "select_question_1": "Series A",
        "boolean_question_1": True,
        "bonus_question_1": "AI/ML"
    }


async def test_comprehensive_scoring():
    """Test the comprehensive scoring engine."""
    print("🚀 Testing Comprehensive Thesis Scoring Engine")
    print("=" * 50)
    
    # Create test data
    thesis = MockThesis()
    thesis.scoring_rules = create_sample_scoring_rules()
    form_responses = create_sample_form_responses()
    
    print(f"📊 Created thesis with {len(thesis.scoring_rules)} scoring rules")
    print(f"📝 Created form responses with {len(form_responses)} answers")
    print()
    
    # Initialize scoring engine
    scoring_engine = ComprehensiveThesisScoringEngine()
    
    # Calculate comprehensive score
    print("🔄 Calculating comprehensive score...")
    result = await scoring_engine.calculate_comprehensive_score(
        thesis_with_rules=thesis,
        form_responses=form_responses,
        form_details=None
    )
    
    # Display results
    print("\n📈 SCORING RESULTS")
    print("=" * 30)
    
    if "error" in result:
        print(f"❌ Error: {result['error']}")
        return
    
    # Thesis scoring
    thesis_data = result.get("thesis", {})
    print(f"🎯 Thesis Score: {thesis_data.get('normalized_score', 0):.1f}/100")
    print(f"   Total Score: {thesis_data.get('total_score', 0):.2f}")
    print(f"   Max Possible: {thesis_data.get('max_possible_score', 0):.2f}")
    print(f"   Rules Processed: {len(thesis_data.get('question_scores', {}))}")
    
    # Question-wise breakdown
    print("\n📋 Question Scores:")
    for question_id, score_data in thesis_data.get("question_scores", {}).items():
        print(f"   {question_id}: {score_data['raw_score']:.2f} (weight: {score_data['weight']}) = {score_data['weighted_score']:.2f}")
        if score_data.get('ai_generated'):
            print(f"      🤖 AI Generated: {score_data.get('explanation', 'No explanation')}")
        else:
            print(f"      📊 Rule-based: {score_data.get('explanation', 'No explanation')}")
    
    # Bonus scores
    bonus_scores = thesis_data.get("bonus_scores", {})
    if bonus_scores:
        print("\n🎁 Bonus Scores:")
        for bonus_id, bonus_data in bonus_scores.items():
            print(f"   {bonus_id}: +{bonus_data['bonus_points']} points")
    
    # Founders and Market (placeholders for now)
    founders_data = result.get("founders", {})
    market_data = result.get("market", {})
    print(f"\n👥 Founders Score: {founders_data.get('normalized_score', 0):.1f}/100")
    print(f"🏪 Market Score: {market_data.get('normalized_score', 0):.1f}/100")
    
    # Metadata
    metadata = result.get("metadata", {})
    print(f"\n📊 Metadata:")
    print(f"   Scoring Version: {metadata.get('scoring_version', 'Unknown')}")
    print(f"   AI Scoring Used: {metadata.get('ai_scoring_used', False)}")
    print(f"   Rules Processed: {metadata.get('total_rules_processed', 0)}")
    
    print("\n✅ Comprehensive scoring test completed!")
    
    # Save result to file for inspection
    with open("scoring_test_result.json", "w") as f:
        # Convert ObjectIds to strings for JSON serialization
        json_result = json.loads(json.dumps(result, default=str))
        json.dump(json_result, f, indent=2)
    
    print("💾 Results saved to scoring_test_result.json")


if __name__ == "__main__":
    print("🧪 TractionX Comprehensive Scoring Test")
    print("Note: Set OPENAI_API_KEY environment variable for AI text scoring")
    print()
    
    asyncio.run(test_comprehensive_scoring())
