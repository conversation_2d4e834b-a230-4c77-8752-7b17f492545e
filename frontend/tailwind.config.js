const { fontFamily } = require("tailwindcss/defaultTheme")

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./app/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./ui/**/*.{ts,tsx}",
    "./content/**/*.{md,mdx}",
  ],
  darkMode: ["class"],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "24px", // Premium card radius matching deal/[id] design
        md: "16px", // Medium radius for smaller cards
        sm: "8px",  // Small radius for buttons/badges
        xl: "24px", // Extra large for main cards
        "2xl": "32px", // For hero sections
      },
      fontFamily: {
        sans: ["Space Grotesk", "var(--font-sans)", ...fontFamily.sans],
        heading: ["Space Grotesk", "var(--font-heading)", ...fontFamily.sans],
      },
      fontSize: {
        'xs': ['12px', { lineHeight: '1.4' }],   // was 14px, now 0.7x = 9.8px ≈ 10px
        'sm': ['13px', { lineHeight: '1.5' }],   // was 16px, now 0.7x = 11.2px ≈ 11px
        'base': ['14px', { lineHeight: '1.6' }], // was 18px, now 0.7x = 12.6px ≈ 13px
        'lg': ['15px', { lineHeight: '1.5' }],   // was 20px, now 0.7x = 14px
        'xl': ['19px', { lineHeight: '1.3' }],   // was 24px, now 0.7x = 16.8px ≈ 17px
        '2xl': ['24px', { lineHeight: '1.2' }],  // was 32px, now 0.7x = 22.4px ≈ 22px
        '3xl': ['26px', { lineHeight: '1.1' }],  // was 36px, now 0.7x = 25.2px ≈ 25px
        '4xl': ['36px', { lineHeight: '1.1' }],  // was 48px, now 0.7x = 33.6px ≈ 34px
      },
      spacing: {
        '18': '4.5rem', // 72px - for consistent section spacing
        '32': '8rem',   // 128px - for large section spacing
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
}
