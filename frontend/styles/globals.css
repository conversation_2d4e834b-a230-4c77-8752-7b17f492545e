@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Premium Form Preview Animations */
  @keyframes shake {
    0%, 100% {
      transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
      transform: translateX(2px);
    }
  }

  .animate-shake {
    animation: shake 0.5s ease-in-out;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-4px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(99, 102, 241, 0.3);
    }
    50% {
      box-shadow: 0 0 20px rgba(99, 102, 241, 0.6);
    }
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;

    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;

    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;

    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;

    --border: 216 34% 17%;
    --input: 216 34% 17%;

    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;

    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --ring: 216 34% 17%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Space Grotesk', system-ui, -apple-system, sans-serif;
  }

  /* Platform-wide typography system (scaled to 0.7x for compact design) */
  h1 {
    @apply text-2xl font-bold leading-tight;
    font-size: 22px; /* was 32px, now 0.7x = 22.4px ≈ 22px */
    line-height: 1.2;
  }

  h2 {
    @apply text-xl font-bold leading-tight;
    font-size: 17px; /* was 24px, now 0.7x = 16.8px ≈ 17px */
    line-height: 1.3;
  }

  h3 {
    @apply text-lg font-semibold leading-tight;
    font-size: 13px; /* was 18px, now 0.7x = 12.6px ≈ 13px */
    line-height: 1.4;
  }

  p, div {
    font-size: 13px; /* was 18px, now 0.7x = 12.6px ≈ 13px */
    line-height: 1.6;
  }

  .text-sm {
    font-size: 11px; /* was 16px, now 0.7x = 11.2px ≈ 11px */
    line-height: 1.5;
  }

  .text-xs {
    font-size: 10px; /* was 14px, now 0.7x = 9.8px ≈ 10px */
    line-height: 1.4;
  }
}
