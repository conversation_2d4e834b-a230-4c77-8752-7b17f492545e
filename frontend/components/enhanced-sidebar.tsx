// "use client"

// import { useState, useEffect } from "react"
// import Link from "next/link"
// import { usePathname } from "next/navigation"
// import { motion, AnimatePresence } from "framer-motion"
// import { 
//   ChevronLeft, 
//   ChevronRight, 
//   Home,
//   FileText,
//   Target,
//   Settings,
//   BarChart3,
//   Wand2
// } from "lucide-react"

// import { SidebarNavItem } from "@/types"
// import { cn } from "@/lib/utils"
// import { useAuth } from "@/lib/auth-context"
// import { UserAvatar } from "@/components/user-avatar"
// import { Button } from "@/components/ui/button"
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu"
// import {
//   Tooltip,
//   TooltipContent,
//   TooltipProvider,
//   TooltipTrigger,
// } from "@/components/ui/tooltip"

// interface EnhancedSidebarProps {
//   items: SidebarNavItem[]
//   isCollapsed: boolean
//   onToggleCollapse: () => void
// }

// // Icon mapping for better control
// const iconMap = {
//   dashboard: Home,
//   form: FileText,
//   post: BarChart3,
//   page: Target,
//   settings: Settings,
// } as const

// export function EnhancedSidebar({ items, isCollapsed, onToggleCollapse }: EnhancedSidebarProps) {
//   const pathname = usePathname()
//   const { user, logout } = useAuth()
//   const [mounted, setMounted] = useState(false)

//   useEffect(() => {
//     setMounted(true)
//   }, [])

//   if (!mounted) return null

//   return (
//     <TooltipProvider>
//       <motion.aside
//         initial={false}
//         animate={{ 
//           width: isCollapsed ? 60 : 240 
//         }}
//         transition={{ 
//           duration: 0.3, 
//           ease: [0.25, 0.46, 0.45, 0.94] 
//         }}
//         className={cn(
//           "relative flex flex-col h-full bg-gradient-to-b from-[#fafbfc] to-[#f8fafd] dark:from-[#0a0b0f] dark:to-[#10131a]",
//           "border-r border-border/50 shadow-[0_4px_24px_rgba(0,0,0,0.06)] dark:shadow-[0_4px_24px_rgba(0,0,0,0.12)]",
//           "backdrop-blur-xl supports-[backdrop-filter]:bg-background/60"
//         )}
//       >
//         {/* Header with Logo */}
//         <div className="flex items-center justify-between px-6 py-6 border-b border-border/30">
//           <AnimatePresence mode="wait">
//             {!isCollapsed ? (
//               <motion.div
//                 key="logo-expanded"
//                 initial={{ opacity: 0, x: -20 }}
//                 animate={{ opacity: 1, x: 0 }}
//                 exit={{ opacity: 0, x: -20 }}
//                 transition={{ duration: 0.2 }}
//                 className="flex items-center gap-3"
//               >
//                 <div className="relative">
//                   <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
//                     <Wand2 className="w-4 h-4 text-white" />
//                   </div>
//                   <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br from-emerald-400 to-cyan-400 rounded-full animate-pulse" />
//                 </div>
//                 <span className="text-[22px] font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
//                   TractionX
//                 </span>
//               </motion.div>
//             ) : (
//               <motion.div
//                 key="logo-collapsed"
//                 initial={{ opacity: 0, scale: 0.8 }}
//                 animate={{ opacity: 1, scale: 1 }}
//                 exit={{ opacity: 0, scale: 0.8 }}
//                 transition={{ duration: 0.2 }}
//                 className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto"
//               >
//                 <Wand2 className="w-4 h-4 text-white" />
//               </motion.div>
//             )}
//           </AnimatePresence>
//         </div>

//         {/* Toggle Button */}
//         <Button
//           variant="ghost"
//           size="sm"
//           onClick={onToggleCollapse}
//           className={cn(
//             "absolute -right-3 top-8 z-20 w-6 h-6 rounded-full p-0",
//             "bg-background/80 backdrop-blur-sm border border-border/50 shadow-sm",
//             "hover:bg-background hover:shadow-md transition-all duration-200"
//           )}
//         >
//           {isCollapsed ? (
//             <ChevronRight className="w-3 h-3" />
//           ) : (
//             <ChevronLeft className="w-3 h-3" />
//           )}
//         </Button>

//         {/* Navigation */}
//         <nav className="flex-1 px-3 py-6 space-y-1">
//           <AnimatePresence>
//             {items.map((item, index) => {
//               const iconKey = item.icon as keyof typeof iconMap
//               const Icon = iconMap[iconKey] || Home
//               const isActive = pathname === item.href
              
//               const linkContent = (
//                 <motion.div
//                   layout
//                   whileHover={{ scale: 1.02 }}
//                   whileTap={{ scale: 0.98 }}
//                   className={cn(
//                     "relative flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all duration-200 group",
//                     "hover:bg-background/50 hover:shadow-sm",
//                     isActive && "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30",
//                     isActive && "shadow-sm border border-blue-100 dark:border-blue-900/30",
//                     item.disabled && "opacity-50 cursor-not-allowed"
//                   )}
//                 >
//                   {/* Active indicator */}
//                   {isActive && (
//                     <motion.div
//                       layoutId="activeIndicator"
//                       className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-purple-600 rounded-r-full"
//                       transition={{ type: "spring", stiffness: 300, damping: 30 }}
//                     />
//                   )}
                  
//                   <div className={cn(
//                     "flex items-center justify-center w-5 h-5 transition-colors duration-200",
//                     isActive ? "text-blue-600 dark:text-blue-400" : "text-muted-foreground group-hover:text-foreground"
//                   )}>
//                     <Icon className="w-5 h-5" />
//                   </div>
                  
//                   <AnimatePresence>
//                     {!isCollapsed && (
//                       <motion.span
//                         initial={{ opacity: 0, x: -10 }}
//                         animate={{ opacity: 1, x: 0 }}
//                         exit={{ opacity: 0, x: -10 }}
//                         transition={{ duration: 0.2 }}
//                         className={cn(
//                           "text-sm font-medium transition-colors duration-200",
//                           isActive ? "text-foreground font-semibold" : "text-muted-foreground group-hover:text-foreground"
//                         )}
//                       >
//                         {item.title}
//                       </motion.span>
//                     )}
//                   </AnimatePresence>

//                   {/* Glow effect for active item */}
//                   {isActive && (
//                     <motion.div
//                       initial={{ opacity: 0 }}
//                       animate={{ opacity: 1 }}
//                       className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-xl"
//                     />
//                   )}
//                 </motion.div>
//               )

//               if (isCollapsed) {
//                 return (
//                   <Tooltip key={index} delayDuration={0}>
//                     <TooltipTrigger asChild>
//                       <Link 
//                         href={item.disabled ? "#" : item.href}
//                         className="block"
//                       >
//                         {linkContent}
//                       </Link>
//                     </TooltipTrigger>
//                     <TooltipContent side="right" className="font-medium">
//                       {item.title}
//                     </TooltipContent>
//                   </Tooltip>
//                 )
//               }

//               return (
//                 <Link
//                   key={index}
//                   href={item.disabled ? "#" : item.href}
//                   className="block"
//                 >
//                   {linkContent}
//                 </Link>
//               )
//             })}
//           </AnimatePresence>
//         </nav>

//         {/* Footer with User */}
//         <div className="px-3 py-4 border-t border-border/30">
//           {!isCollapsed ? (
//             <DropdownMenu>
//               <DropdownMenuTrigger asChild>
//                 <motion.button
//                   whileHover={{ scale: 1.02 }}
//                   whileTap={{ scale: 0.98 }}
//                   className="w-full flex items-center gap-3 px-3 py-2.5 rounded-xl hover:bg-background/50 transition-all duration-200 group"
//                 >
//                   <UserAvatar
//                     user={{ 
//                       name: user?.name || null, 
//                       image: user?.image || null 
//                     }}
//                     className="w-8 h-8 ring-2 ring-background shadow-sm"
//                   />
//                   <div className="flex-1 text-left min-w-0">
//                     <div className="text-sm font-medium text-foreground truncate">
//                       {user?.name || 'User'}
//                     </div>
//                     <div className="text-xs text-muted-foreground truncate">
//                       {user?.email}
//                     </div>
//                   </div>
//                   <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-foreground transition-colors" />
//                 </motion.button>
//               </DropdownMenuTrigger>
//               <DropdownMenuContent align="end" className="w-56">
//                 <div className="flex items-center justify-start gap-2 p-2">
//                   <div className="flex flex-col space-y-1 leading-none">
//                     <p className="font-medium">{user?.name}</p>
//                     <p className="text-sm text-muted-foreground truncate">
//                       {user?.email}
//                     </p>
//                   </div>
//                 </div>
//                 <DropdownMenuSeparator />
//                 <DropdownMenuItem asChild>
//                   <Link href="/dashboard">Dashboard</Link>
//                 </DropdownMenuItem>
//                 <DropdownMenuItem asChild>
//                   <Link href="/dashboard/settings">Settings</Link>
//                 </DropdownMenuItem>
//                 <DropdownMenuSeparator />
//                 <DropdownMenuItem
//                   className="cursor-pointer text-red-600 focus:text-red-600"
//                   onSelect={(event) => {
//                     event.preventDefault()
//                     logout()
//                   }}
//                 >
//                   Sign out
//                 </DropdownMenuItem>
//               </DropdownMenuContent>
//             </DropdownMenu>
//           ) : (
//             <Tooltip delayDuration={0}>
//               <TooltipTrigger asChild>
//                 <DropdownMenu>
//                   <DropdownMenuTrigger asChild>
//                     <button className="w-full flex justify-center">
//                       <UserAvatar
//                         user={{ 
//                           name: user?.name || null, 
//                           image: user?.image || null 
//                         }}
//                         className="w-8 h-8 ring-2 ring-background shadow-sm hover:ring-blue-200 transition-all duration-200"
//                       />
//                     </button>
//                   </DropdownMenuTrigger>
//                   <DropdownMenuContent align="end" className="w-56">
//                     <div className="flex items-center justify-start gap-2 p-2">
//                       <div className="flex flex-col space-y-1 leading-none">
//                         <p className="font-medium">{user?.name}</p>
//                         <p className="text-sm text-muted-foreground truncate">
//                           {user?.email}
//                         </p>
//                       </div>
//                     </div>
//                     <DropdownMenuSeparator />
//                     <DropdownMenuItem asChild>
//                       <Link href="/dashboard">Dashboard</Link>
//                     </DropdownMenuItem>
//                     <DropdownMenuItem asChild>
//                       <Link href="/dashboard/settings">Settings</Link>
//                     </DropdownMenuItem>
//                     <DropdownMenuSeparator />
//                     <DropdownMenuItem
//                       className="cursor-pointer text-red-600 focus:text-red-600"
//                       onSelect={(event) => {
//                         event.preventDefault()
//                         logout()
//                       }}
//                     >
//                       Sign out
//                     </DropdownMenuItem>
//                   </DropdownMenuContent>
//                 </DropdownMenu>
//               </TooltipTrigger>
//               <TooltipContent side="right" className="font-medium">
//                 {user?.name}
//               </TooltipContent>
//             </Tooltip>
//           )}
          
//           {/* Footer text */}
//           <AnimatePresence>
//             {!isCollapsed && (
//               <motion.div
//                 initial={{ opacity: 0 }}
//                 animate={{ opacity: 1 }}
//                 exit={{ opacity: 0 }}
//                 transition={{ delay: 0.1 }}
//                 className="mt-4 px-3"
//               >
//                 <p className="text-xs text-muted-foreground text-center">
//                   Powered by TractionX
//                 </p>
//               </motion.div>
//             )}
//           </AnimatePresence>
//         </div>
//       </motion.aside>
//     </TooltipProvider>
//   )
// } 

"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { 
  ChevronLeft, 
  ChevronRight, 
  Home,
  FileText,
  Target,
  Settings,
  BarChart3,
  Wand2
} from "lucide-react"

import { SidebarNavItem } from "@/types"
import { cn } from "@/lib/utils"
import { useAuth } from "@/lib/auth-context"
import { UserAvatar } from "@/components/user-avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface EnhancedSidebarProps {
  items: SidebarNavItem[]
  isCollapsed: boolean
  onToggleCollapse: () => void
}

// Icon mapping for better control
const iconMap = {
  dashboard: Home,
  form: FileText,
  post: BarChart3,
  page: Target,
  settings: Settings,
} as const

export function EnhancedSidebar({ items, isCollapsed, onToggleCollapse }: EnhancedSidebarProps) {
  const pathname = usePathname()
  const { user, logout } = useAuth()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <TooltipProvider>
      <motion.aside
        initial={false}
        animate={{ width: isCollapsed ? 60 : 240 }}
        transition={{ 
          duration: 0.3, 
          ease: [0.25, 0.46, 0.45, 0.94] 
        }}
        className={cn(
          "fixed left-0 top-0 z-40 h-screen flex flex-col min-h-screen",
          // **Premium gradient - more distinct and sophisticated**
          "bg-gradient-to-b from-[#F4F7FB] to-[#E8EBF0] dark:from-[#181C22] dark:to-[#101217]",
          "border-r border-border/40 shadow-none",
          "backdrop-blur-xl supports-[backdrop-filter]:bg-background/80"
        )}
      >
        {/* Header with Logo */}
        <div className="flex items-center justify-between px-6 py-6 border-b border-border/30">
          <AnimatePresence mode="wait">
            {!isCollapsed ? (
              <motion.div
                key="logo-expanded"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="flex items-center gap-3"
              >
                {/* 
                  Replace with your real SVG/PNG logo when available
                  e.g. <img src="/logo.svg" ... /> 
                */}
                <div className="relative">
                  <div className="w-8 h-8 rounded-xl flex items-center justify-center bg-gradient-to-br from-[#D4D7DF] to-[#F0F1F6] dark:from-[#23232B] dark:to-[#17181c]">
                    <Wand2 className="w-4 h-4 text-[#717184] dark:text-white" />
                  </div>
                  {/* Optional status dot or nothing */}
                  {/* <div className="absolute -top-1 -right-1 w-3 h-3 bg-gray-300 rounded-full" /> */}
                </div>
                <span className="text-[22px] font-bold text-gray-900 dark:text-gray-100 tracking-tight">
                  TractionX
                </span>
              </motion.div>
            ) : (
              <motion.div
                key="logo-collapsed"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
                className="w-8 h-8 rounded-xl flex items-center justify-center bg-gradient-to-br from-[#D4D7DF] to-[#F0F1F6] dark:from-[#23232B] dark:to-[#17181c] mx-auto"
              >
                <Wand2 className="w-4 h-4 text-[#717184] dark:text-white" />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Toggle Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleCollapse}
          className={cn(
            "absolute -right-3 top-8 z-20 w-6 h-6 rounded-full p-0",
            "bg-background/80 backdrop-blur-sm border border-border/50",
            "hover:bg-background transition-all duration-200"
          )}
        >
          {isCollapsed ? (
            <ChevronRight className="w-3 h-3 text-muted-foreground" />
          ) : (
            <ChevronLeft className="w-3 h-3 text-muted-foreground" />
          )}
        </Button>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-6 space-y-1">
          <AnimatePresence>
            {items.map((item, index) => {
              const iconKey = item.icon as keyof typeof iconMap
              const Icon = iconMap[iconKey] || Home
              const isActive = pathname === item.href
              
              const linkContent = (
                <motion.div
                  layout
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={cn(
                    "relative flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all duration-200 group",
                    "hover:bg-muted/50 hover:shadow-sm",
                    isActive && "bg-muted/60 border border-border/30",
                    item.disabled && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {/* Active indicator (minimal, no color bar) */}
                  {isActive && (
                    <motion.div
                      layoutId="activeIndicator"
                      className="absolute left-0 top-0 bottom-0 w-1 bg-accent rounded-r-full"
                      transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    />
                  )}
                  
                  <div className={cn(
                    "flex items-center justify-center w-5 h-5 transition-colors duration-200",
                    isActive ? "text-accent-foreground" : "text-muted-foreground group-hover:text-foreground"
                  )}>
                    <Icon className="w-5 h-5" />
                  </div>
                  
                  <AnimatePresence>
                    {!isCollapsed && (
                      <motion.span
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                        transition={{ duration: 0.2 }}
                        className={cn(
                          "text-sm font-medium transition-colors duration-200",
                          isActive ? "text-foreground font-semibold" : "text-muted-foreground group-hover:text-foreground"
                        )}
                      >
                        {item.title}
                      </motion.span>
                    )}
                  </AnimatePresence>
                </motion.div>
              )

              if (isCollapsed) {
                return (
                  <Tooltip key={index} delayDuration={0}>
                    <TooltipTrigger asChild>
                                           <Link 
                       href={item.disabled ? "#" : (item.href || "#")}
                       className="block"
                     >
                        {linkContent}
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="font-medium">
                      {item.title}
                    </TooltipContent>
                  </Tooltip>
                )
              }

              return (
                                 <Link
                   key={index}
                   href={item.disabled ? "#" : (item.href || "#")}
                   className="block"
                 >
                  {linkContent}
                </Link>
              )
            })}
          </AnimatePresence>
        </nav>

        {/* Footer with User */}
        <div className="px-3 py-4 border-t border-border/30">
          {!isCollapsed ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full flex items-center gap-3 px-3 py-2.5 rounded-xl hover:bg-background/50 transition-all duration-200 group"
                >
                  <UserAvatar
                    user={{ 
                      name: user?.name || null, 
                      image: null 
                    }}
                    className="w-8 h-8 ring-2 ring-background shadow-sm"
                  />
                  <div className="flex-1 text-left min-w-0">
                    <div className="text-sm font-medium text-foreground truncate">
                      {user?.name || 'User'}
                    </div>
                    <div className="text-xs text-muted-foreground truncate">
                      {user?.email}
                    </div>
                  </div>
                  <ChevronRight className="w-4 h-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                </motion.button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <div className="flex items-center justify-start gap-2 p-2">
                  <div className="flex flex-col space-y-1 leading-none">
                    <p className="font-medium">{user?.name}</p>
                    <p className="text-sm text-muted-foreground truncate">
                      {user?.email}
                    </p>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard">Dashboard</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/settings">Settings</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="cursor-pointer text-red-600 focus:text-red-600"
                  onSelect={(event) => {
                    event.preventDefault()
                    logout()
                  }}
                >
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button className="w-full flex justify-center">
                      <UserAvatar
                        user={{ 
                          name: user?.name || null, 
                          image: null 
                        }}
                        className="w-8 h-8 ring-2 ring-background shadow-sm hover:ring-blue-200 transition-all duration-200"
                      />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <div className="flex items-center justify-start gap-2 p-2">
                      <div className="flex flex-col space-y-1 leading-none">
                        <p className="font-medium">{user?.name}</p>
                        <p className="text-sm text-muted-foreground truncate">
                          {user?.email}
                        </p>
                      </div>
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard">Dashboard</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard/settings">Settings</Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="cursor-pointer text-red-600 focus:text-red-600"
                      onSelect={(event) => {
                        event.preventDefault()
                        logout()
                      }}
                    >
                      Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TooltipTrigger>
              <TooltipContent side="right" className="font-medium">
                {user?.name}
              </TooltipContent>
            </Tooltip>
          )}
          
          {/* Footer text */}
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ delay: 0.1 }}
                className="mt-4 px-3"
              >
                <p className="text-xs text-muted-foreground text-center">
                  Powered by TractionX
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.aside>
    </TooltipProvider>
  )
}
