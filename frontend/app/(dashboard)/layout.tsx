"use client"

import { dashboardConfig } from "@/config/dashboard"
import { MainNav } from "@/components/main-nav"
import { SiteFooter } from "@/components/site-footer"
import { UserAccountNav } from "@/components/user-account-nav"
import { ProtectedRoute } from "@/components/protected-route"
import { OrganizationSelector } from "@/components/org-selector"
import { EnhancedSidebar } from "@/components/enhanced-sidebar"
import { useAuth } from "@/lib/auth-context"
import { useState } from "react"

interface DashboardLayoutProps {
  children?: React.ReactNode
}

export default function DashboardLayout({
  children,
}: DashboardLayoutProps) {
  const { user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen flex-col space-y-6">
        <header className={`sticky top-0 z-30 border-b bg-background transition-all duration-300 ${
          sidebarOpen ? 'md:ml-[240px]' : 'md:ml-[60px]'
        }`}>
          <div className="flex h-16 items-center justify-between py-4 px-8">
            <div className="flex items-center gap-4">
              {/* Mobile Menu Button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="md:hidden p-2 rounded-lg hover:bg-muted transition-colors"
                aria-label="Toggle mobile menu"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={mobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
                  />
                </svg>
              </button>
              <MainNav items={dashboardConfig.mainNav} />
              <OrganizationSelector />
            </div>
            {user && (
              <UserAccountNav
                user={{
                  name: user.name,
                  image: null,
                  email: user.email,
                }}
              />
            )}
          </div>
        </header>
        {/* Enhanced Sidebar - Fixed Positioning */}
        <div className="hidden md:block">
          <EnhancedSidebar
            items={dashboardConfig.sidebarNav}
            isCollapsed={!sidebarOpen}
            onToggleCollapse={() => setSidebarOpen((open) => !open)}
          />
        </div>

        {/* Mobile Sidebar Overlay */}
        {mobileMenuOpen && (
          <>
            <div 
              className="fixed inset-0 bg-black/50 z-30 md:hidden"
              onClick={() => setMobileMenuOpen(false)}
            />
            <div className="fixed left-0 top-0 h-full w-[240px] z-50 md:hidden">
              <EnhancedSidebar
                items={dashboardConfig.sidebarNav}
                isCollapsed={false}
                onToggleCollapse={() => setMobileMenuOpen(false)}
              />
            </div>
          </>
        )}

        {/* Main Content - Account for Fixed Sidebar */}
        <main 
          className={`flex-1 min-h-screen px-8 py-6 overflow-x-hidden transition-all duration-300 border-l border-border/20 ${
            sidebarOpen ? 'md:ml-[240px]' : 'md:ml-[60px]'
          }`}
          style={{ minHeight: 'calc(100vh - 4rem)' }}
        >
          {children}
        </main>
              <SiteFooter 
          className={`border-t transition-all duration-300 ${
            sidebarOpen ? 'md:ml-[240px]' : 'md:ml-[60px]'
          }`} 
        />
    </div>
    </ProtectedRoute>
  )
}
